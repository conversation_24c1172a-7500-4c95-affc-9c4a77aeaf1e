-- AlterTable
ALTER TABLE "User" ADD COLUMN     "banned" BO<PERSON>EAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "PlayCount" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "gameId" TEXT NOT NULL,
    "count" INTEGER NOT NULL,

    CONSTRAINT "PlayCount_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "PlayCount" ADD CONSTRAINT "PlayCount_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
