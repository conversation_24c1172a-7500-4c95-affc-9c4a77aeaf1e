# Wilkinson 2025

This repository contains the code for the Wilkinson 2025 web application.

## Features

- **Games:** Play various interactive games (Balance, Catch, Quiz).
- **User Authentication:** Secure login using NextAuth.
- **Leaderboard:** View top scores and rankings.
- **Personal Profile:** Manage user information.
- **Prize Form:** Submit information for prize redemption.
- **Admin Panel:** Manage coupons, view daily winners, draw records, and user records.

## Technologies Used

- Next.js
- React
- TypeScript
- Prisma (ORM)
- NextAuth (Authentication)

## API Endpoints

The application provides the following API endpoints:

- `/api/admin/*`: Endpoints for administrative tasks.
- `/api/auth/*`: Authentication related endpoints.
- `/api/coupon/*`: Manage user coupons.
- `/api/game/*`: Game related data and actions.
- `/api/prize-form/*`: Handle prize form submissions.
- `/api/record/*`: Manage user game records.
- `/api/user/*`: Manage user profile information.
