{"name": "corporate-sports-event", "version": "0.1.0", "private": true, "scripts": {"gen": "prisma generate", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@auth/prisma-adapter": "^2.7.4", "@next/third-parties": "^15.3.3", "@prisma/client": "^6.4.1", "@tailwindcss/postcss": "^4.1.7", "@tanstack/react-query": "^5.64.1", "@types/react-google-recaptcha": "^2.1.9", "antd": "^5.25.4", "clsx": "^2.1.1", "dayjs": "^1.11.13", "export-html-table-to-csv": "^1.0.4", "is-ua-webview": "^1.1.2", "next": "^15.1.5", "next-auth": "^5.0.0-beta.25", "noisejs": "^2.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-google-recaptcha": "^3.1.0", "react-icons": "^5.3.0", "sass": "^1.89.0", "sharp": "^0.34.1", "simplex-noise": "^4.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tanstack/react-query-devtools": "^5.79.0", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/noisejs": "^2.1.0", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9.26.0", "eslint-config-next": "15.0.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "jest": "^29.7.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prisma": "^6.4.1", "tailwindcss": "^4.1.7", "ts-jest": "^29.2.5", "typescript": "^5"}}