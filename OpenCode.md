## Project Analysis

This is a Next.js project using:
- TypeScript
- React
- Prisma (for database)
- Tailwind CSS (for styling)
- Jest (for testing)

The project structure includes:
- `app/`: Pages and API routes
- `components/`: Reusable UI components
- `libs/`: Utility functions and libraries
- `prisma/`: Database schema and migrations
- `utils/`: Helper functions
- `public/`: Static assets

## Useful Commands

- Lint: `npm run lint`
- Typecheck: `npm run typecheck`
- Test: `npm test`

## Testing Practices

- Tests are written using Jest.
- Test files are located in `__tests__` directories, often alongside the code they are testing.
- Test files follow the naming convention `*.test.ts`.
