"use client";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function AuthError() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");
  const [isIncognitoMode, setIsIncognitoMode] = useState(false);

  useEffect(() => {
    // Detect incognito mode
    const detectIncognito = () => {
      try {
        // Multiple methods to detect incognito mode
        const isIncognito =
          !window.indexedDB ||
          !window.localStorage ||
          !window.sessionStorage ||
          (window.navigator.storage && !window.navigator.storage.estimate);

        setIsIncognitoMode(isIncognito);
      } catch {
        setIsIncognitoMode(true);
      }
    };

    detectIncognito();
  }, []);

  const getErrorMessage = () => {
    switch (error) {
      case "Configuration":
        return {
          title: "設定錯誤",
          description: "伺服器設定發生問題，請稍後再試。",
          isServerIssue: true,
        };
      case "AccessDenied":
        return {
          title: "存取被拒絕",
          description: "您沒有權限存取此資源。",
          isServerIssue: false,
        };
      case "Verification":
        return {
          title: "驗證失敗",
          description: "無法驗證您的身份，請重新登入。",
          isServerIssue: false,
        };
      case "Default":
      default:
        if (isIncognitoMode) {
          return {
            title: "無痕模式登入問題",
            description: "偵測到您正在使用無痕模式，這可能會影響登入功能。",
            isServerIssue: false,
          };
        }
        return {
          title: "登入發生錯誤",
          description: "登入過程中發生未知錯誤，請重新嘗試。",
          isServerIssue: false,
        };
    }
  };

  const errorInfo = getErrorMessage();

  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-[var(--primary-color)] text-white px-4">
      <div className="w-full max-w-md text-center">
        {/* Error Icon */}
        <div className="mb-8">
          <div className="w-20 h-20 mx-auto flex items-center justify-center">
            <svg
              className="w-10 h-10 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
        </div>

        {/* Error Title */}
        <h1 className="text-2xl font-bold mb-4">{errorInfo.title}</h1>

        {/* Error Description */}
        <p className="text-lg mb-6">{errorInfo.description}</p>

        {/* Additional Help */}
        <div className="mt-8 text-xs text-white/70">
          {process.env.NODE_ENV === "development" && error && (
            <p className="mt-2">錯誤代碼: {error}</p>
          )}
        </div>
      </div>
    </div>
  );
}
