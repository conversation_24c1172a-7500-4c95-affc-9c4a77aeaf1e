import { useState, useCallback } from "react";
import { imageUrl } from "@/utils/image-url";

export const INIT_LOADING_STATE = {
  finished: 0,
  nickname: "",
};

// All images used throughout the game experience
const GAME_IMAGES = [
  "/btn-game1-play-again.png",
  "/btn-game1-play-now.png",
  "/btn-game2-coming-soon.png",
  "/btn-game2-play-again.png",
  "/btn-game2-play-now.png",
  "/btn-game3-coming-soon.png",
  "/btn-game3-play-again.png",
  "/btn-game3-play-now.png",
  "/button-app.png",
  "/button-buy.png",
  "/button-dialog.png",
  "/button-logo-momo.png",
  "/button-logo-pchome.png",
  "/button-wide.png",
  "/dialog-steps.png",
  "/dialog-title-all.png",
  "/dialog-title-catch.png",
  "/dialog-title-quiz.png",
  "/favicon.png",
  "/frame-full.png",
  "/game-balance-cap.png",
  "/game-balance-direction.png",
  "/game-balance-water.png",
  "/game-catch-asset-1.png",
  "/game-catch-asset-2.png",
  "/game-catch-asset-3.png",
  "/game-catch-asset-4.png",
  "/game-catch-asset-5.png",
  "/game-catch-asset-6.png",
  "/game-catch-asset-7.png",
  "/game-catch-asset-8.png",
  "/game-catch-asset-9.png",
  "/game-instruction-dialog.png",
  "/game-quiz-option-result.png",
  "/game-quiz-option.png",
  "/game-result-background.png",
  "/game-score-background.png",
  "/game-title-balance.png",
  "/game-title-catch.png",
  "/game-title-quiz.png",
  "/login-facebook.png",
  "/login-line.png",
  "/logo-button.png",
  "/logo-facebook.png",
  "/logo-wilkinson.png",
  "/menu-background.png",
  "/popup-game-common.png",
  "/popup-game-complete-all.png",
  "/popup-luckydraw-reward.png",
  "/popup-normal.png",
  "/popup-small.png",
  "/prize-1st-title.png",
  "/prize-1st.png",
  "/screen-channel.png",
  "/screen-cm.png",
  "/screen-game-entries.png",
  "/screen-game.png",
  "/screen-loading.png",
  "/screen-login.png",
  "/screen-page.png",
  "/screen-start.png",
  "/spark.png",
  "/tab-active.png",
  "/tab-game-balance.png",
  "/tab-game-catch.png",
  "/tab-game-quiz.png",
  "/tab-normal.png",
  "/text-texture-white.png",
  "/text-texture-yellow.png",
  "/voucher-background.png",
  "/voucher-bottle.png",
];

interface PreloaderState {
  isLoading: boolean;
  progress: number;
  loadedCount: number;
  totalCount: number;
  isComplete: boolean;
  hasStarted: boolean;
  startTime: number;
}

export const useImagePreloader = () => {
  const [state, setState] = useState<PreloaderState>({
    isLoading: false,
    progress: 0,
    loadedCount: 0,
    totalCount: GAME_IMAGES.length,
    isComplete: false,
    hasStarted: false,
    startTime: 0,
  });

  const preloadImages = useCallback(() => {
    const startTime = Date.now();
    setState((prev) => {
      if (prev.hasStarted) return prev; // Prevent multiple starts
      return {
        ...prev,
        isLoading: true,
        isComplete: false,
        hasStarted: true,
        startTime,
      };
    });

    let loadedCount = 0;
    const totalCount = GAME_IMAGES.length;
    const MIN_LOADING_TIME = 1000; // Minimum 1 seconds

    const checkCompletion = () => {
      const elapsedTime = Date.now() - startTime;
      const allImagesLoaded = loadedCount === totalCount;
      const minTimeElapsed = elapsedTime >= MIN_LOADING_TIME;

      if (allImagesLoaded) {
        INIT_LOADING_STATE.finished += 1;

        if (minTimeElapsed) {
          setState((prev) => ({
            ...prev,
            isComplete: true,
            isLoading: false,
          }));
        } else if (!minTimeElapsed) {
          // Wait for minimum time to elapse
          setTimeout(() => {
            setState((prev) => ({
              ...prev,
              isComplete: true,
              isLoading: false,
            }));
          }, MIN_LOADING_TIME - elapsedTime);
        }
      }
    };

    const updateProgress = () => {
      loadedCount++;
      const progress = Math.round((loadedCount / totalCount) * 100);

      setState((prev) => ({
        ...prev,
        loadedCount,
        progress,
      }));

      checkCompletion();
    };

    // Preload all images
    GAME_IMAGES.forEach((imagePath) => {
      const img = new Image();

      img.onload = updateProgress;
      img.onerror = updateProgress; // Still count as "loaded" to prevent hanging

      img.src = imageUrl(imagePath);
    });
  }, []);

  return {
    ...state,
    preloadImages,
  };
};
