"use client";
import { useQuery } from "@tanstack/react-query";

// 檢查用戶是否完成全部遊戲
const checkGameCompletion = async () => {
  const response = await fetch("/api/game/list");
  const { gameList, hasCompletePrizeForm } = await response.json();
  const allGamesComplete = Object.keys(gameList).every(
    (gameId) => gameList[gameId].GameRecord.length > 0,
  );

  return {
    allGamesComplete,
    hasCompletePrizeForm,
  };
};

export const useCheckGameCompletion = () => {
  const { data } = useQuery({
    queryFn: checkGameCompletion,
    queryKey: ["game", "completion"],
  });

  return data;
};
