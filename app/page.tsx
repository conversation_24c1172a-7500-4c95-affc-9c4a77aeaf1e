"use client";

import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from "@tanstack/react-query";
import { WebsiteConfig } from "./game-control-dashboard/services/configService";
import { redirect } from "next/navigation";

const Page = () => {
  const { data } = useQuery({
    queryKey: ["siteInfo"],
    queryFn: () =>
      fetch("/api/event").then((res) => res.json()) as Promise<WebsiteConfig>,
  });

  const { startDate } = data ?? {};

  if (!startDate) {
    return null;
  }

  if (new Date(startDate).getTime() < Date.now()) {
    redirect("/home");
  }

  return (
    <div
      className="pt-[20vh] text-center"
      style={{
        color: "white",
        fontSize: "28px",
      }}
    >
      威金森超激夏祭り活動
      <br />
      即將於7月中開跑，敬請期待
    </div>
  );
};

export default function RootPage() {
  return (
    <QueryClientProvider client={new QueryClient()}>
      <Page />
    </QueryClientProvider>
  );
}
