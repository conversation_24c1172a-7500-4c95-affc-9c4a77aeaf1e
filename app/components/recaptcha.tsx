"use client";
import { useEffect, useRef } from "react";
import ReCA<PERSON><PERSON><PERSON> from "react-google-recaptcha";

interface RecaptchaProps {
  onVerify: (token: string | null) => void;
  size?: "compact" | "normal" | "invisible";
  theme?: "light" | "dark";
  className?: string;
}

export const Recaptcha = ({
  onVerify,
  size = "normal",
  theme = "light",
  className,
}: RecaptchaProps) => {
  const recaptchaRef = useRef<ReCAPTCHA>(null);

  // Get site key from environment variable
  const siteKey = process.env.NEXT_PUBLIC_GOOGLE_RECAPTCHA_SITE_KEY;

  useEffect(() => {
    if (!siteKey) {
      console.error("NEXT_PUBLIC_GOOGLE_RECAPTCHA_SITE_KEY is not configured");
    }
  }, [siteKey]);

  const handleChange = (token: string | null) => {
    onVerify(token);
  };

  const handleExpired = () => {
    onVerify(null);
    alert("人機驗證已過期，請重新驗證");
  };

  const handleError = () => {
    onVerify(null);
    alert("人機驗證發生錯誤，請重試");
  };

  if (!siteKey) {
    return (
      <div className="text-red-500 text-sm">reCAPTCHA configuration error</div>
    );
  }

  return (
    <div className={className}>
      <ReCAPTCHA
        hl="zh-TW"
        ref={recaptchaRef}
        sitekey={siteKey}
        onChange={handleChange}
        onExpired={handleExpired}
        onError={handleError}
        size={size}
        theme={theme}
      />
    </div>
  );
};

// Export the ref methods for external control
export type RecaptchaRef = {
  reset: () => void;
  execute: () => void;
};
