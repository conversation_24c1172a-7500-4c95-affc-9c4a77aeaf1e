import { imageUrl } from "@/utils/image-url";
import { ButtonBase, ButtonBaseProps } from "./button-base";
import { TexturedText } from "../textured-text";

export const DialogButton = (
  props: Omit<ButtonBaseProps, "sizeClass" | "backgroundImage" | "color">,
) => {
  const { children, ...restProps } = props;
  return (
    <ButtonBase
      sizeClass="w-[33vw] aspect-[3]"
      backgroundImage={{
        src: imageUrl("/button-dialog.png"),
        width: 399,
        height: 165,
        sizeClass: "min-w-[115%]",
      }}
      {...restProps}
    >
      <TexturedText
        className="text-[5.7vw] font-[1000] textured-text"
        color="yellow"
      >
        {children}
      </TexturedText>
    </ButtonBase>
  );
};
