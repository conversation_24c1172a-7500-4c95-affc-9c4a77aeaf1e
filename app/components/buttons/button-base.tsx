import Image from "next/image";
import { PropsWithChildren } from "react";
import clsx from "clsx";

export type ButtonBaseProps = PropsWithChildren<{
  as?: "button" | "div";
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
  sizeClass: string;
  backgroundImage: {
    src: string;
    width: number;
    height: number;
    sizeClass: string;
  };
}>;

export const ButtonBase = ({
  children,
  as = "button",
  disabled,
  onClick,
  className,
  sizeClass,
  backgroundImage,
}: ButtonBaseProps) => {
  const Component = as;

  return (
    <Component
      onClick={onClick}
      className={clsx(
        "relative z-0 active:opacity-80 flex justify-center items-center cursor-pointer",
        { ["opacity-50 pointer-events-none cursor-not-allowed"]: disabled },
        sizeClass,
        className,
      )}
    >
      <Image
        unoptimized
        priority
        src={backgroundImage.src}
        className={clsx(
          backgroundImage.sizeClass,
          "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 -z-10",
        )}
        width={backgroundImage.width}
        height={backgroundImage.height}
        alt=""
      />
      {children}
    </Component>
  );
};
