import clsx from "clsx";
import { PropsWithChildren } from "react";

export const Checkbox = ({
  children,
  checked,
  className,
  onChange,
}: PropsWithChildren<{
  checked: boolean;
  className?: string;
  onChange: (checked: boolean) => void;
}>) => {
  return (
    <label className={clsx(className, "flex items-center")}>
      <input
        className="hidden"
        type="checkbox"
        checked={checked}
        onChange={(e) => {
          onChange(e.target.checked);
        }}
      />
      <span className="w-[4.6vw] h-[4.6vw] inline-block border-1 rounded-[1.5vw] border-black bg-black/50 mr-[0.5em] font-medium">
        <div className="flex justify-center items-center leading-[1em] text-[3.3vw]">
          {checked ? "✔︎" : ""}
        </div>
      </span>
      {children}
    </label>
  );
};
