import { imageUrl } from "@/utils/image-url";
import Image from "next/image";
import { PropsWithChildren } from "react";
import { Menu } from "../menu";
import Link from "next/link";

export const MobileLayout = ({ children }: PropsWithChildren) => {
  return (
    <div className="w-full aspect-9/16 relative bg-black">
      <div className="relative z-0 w-full h-full pt-[23vw] pb-[6vw] px-[10vw]">
        {children}
      </div>
      <Image
        unoptimized
        className="absolute z-10 top-0 left-0 w-full h-full object-cover"
        alt=""
        src={imageUrl("/frame-full.png")}
        width={1080}
        height={1920}
      />
      <Link
        href="/home"
        className="w-[20vw] h-[20vw] absolute top-[5vw] left-0 right-0 mx-auto z-50"
        id="headerLogo"
      />
      <Menu />
    </div>
  );
};
