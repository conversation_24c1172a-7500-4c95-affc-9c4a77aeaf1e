import { FullScreenImage } from "@/app/components/full-screen";
import { FacebookLogin, LineLogin } from "@/app/components/login-buttons";
import { TexturedText } from "@/app/components/textured-text";

import { imageUrl } from "@/utils/image-url";
import Script from "next/script";

export const LoginScreen = () => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-login.png")} />

      <div className="flex flex-col items-center relative pt-[56.5vw]">
        <TexturedText className="font-[1000] text-[5.5vw]">
          登入開始遊戲
        </TexturedText>

        <div className="flex flex-col items-center gap-[3vw] pt-[2vw]">
          <LineLogin />
          <FacebookLogin />
          <Script id={"login-script" + Date.now()}>
            {`
            lineLogin.addEventListener("click", () => {
              console.log("登入_LINE");
              window.gtag("event", "click", { event_label: "登入_LINE" });
            })
            fbLogin.addEventListener("click", () => {
              console.log("登入_FB")
              window.gtag("event", "click", { event_label: "登入_FB" });
            })
            `}
          </Script>
        </div>
      </div>
    </>
  );
};
