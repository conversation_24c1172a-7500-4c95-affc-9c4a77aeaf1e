"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import isWebView from "is-ua-webview";
import { imageUrl } from "@/utils/image-url";
import { AppButton } from "@/app/components/buttons/app-button";
import { DialogBase } from "./dialog-base";
import { isLineWebView } from "@/utils/is-line-webview";

export const DialogOpenInBrowser = () => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    setVisible(
      isWebView(navigator.userAgent) || isLineWebView(navigator.userAgent),
    );
  }, []);

  const onClose = () => {
    setVisible(false);
  };

  if (!visible) return null;

  return (
    <DialogBase visible={visible}>
      <div className="w-[72vw] aspect-[778/625] relative z-0 flex flex-col items-center text-center pt-[9vw]">
        <Image
          unoptimized
          priority
          src={imageUrl("/popup-small.png")}
          alt=""
          className="absolute -z-10 top-0 left-0 w-full h-full ml-[1.5vw]"
          unselectable="on"
          width={778}
          height={625}
        />
        <div className="text-[4.2vw] font-bold tracking-tight mb-[3vw]">
          為確保活動順暢進行
          <br />
          請使用Chrome或其他瀏覽器
        </div>
        <Image
          unoptimized
          priority
          src={imageUrl("/dialog-steps.png")}
          className="w-[55vw] mb-[5vw]"
          alt=""
          width={587}
          height={119}
        />

        <AppButton
          className="max-w-[32vw]"
          onClick={() => {
            try {
              navigator.clipboard.writeText(window.location.href);
              alert("連結已複製");
            } catch {
              alert("複製連結失敗");
            }
          }}
        >
          複製連結
        </AppButton>

        <button
          className="absolute top-0 right-0 w-[9vw] h-[9vw]"
          onClick={onClose}
        />
      </div>
    </DialogBase>
  );
};
