import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  userRecordService,
  type UserRecordsParams,
} from "../services/userRecordService";

// Query Keys
export const userRecordKeys = {
  all: ["userRecords"] as const,
  lists: () => [...userRecordKeys.all, "list"] as const,
  list: (params: UserRecordsParams) =>
    [...userRecordKeys.lists(), params] as const,
};

// 獲取用戶記錄列表的 hook
export const useUserRecords = (params: UserRecordsParams) => {
  return useQuery({
    queryKey: userRecordKeys.list(params),
    queryFn: () => userRecordService.getUserRecords(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// 手動重新獲取用戶記錄數據的 hook
export const useRefreshUserRecords = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({
      queryKey: userRecordKeys.lists(),
    });
  };
};
