"use client";
import React, { useState, useMemo, useEffect } from "react";
import {
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  Statistic,
  Row,
  Col,
  Card,
} from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  DownloadOutlined,
  GiftOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import {
  useCoupons,
  useCreateCoupons,
  useRefreshCoupons,
} from "../hooks/useCoupons";
import type { CouponRecord } from "../services/couponService";

const { Search } = Input;
const { Option } = Select;
const { TextArea } = Input;

const columns: ColumnsType<CouponRecord> = [
  {
    title: "獎券代碼",
    dataIndex: "code",
    key: "code",
    width: 150,
    render: (code: string) => <span className="font-mono text-sm">{code}</span>,
  },
  {
    title: "狀態",
    dataIndex: "status",
    key: "status",
    width: 100,
    render: (status: string) => {
      const statusConfig = {
        available: {
          color: "green",
          text: "可用",
          icon: <CheckCircleOutlined />,
        },
        assigned: {
          color: "orange",
          text: "已分配",
          icon: <ClockCircleOutlined />,
        },
      };
      const config = statusConfig[status as keyof typeof statusConfig];
      return (
        <Tag color={config.color} icon={config.icon}>
          {config.text}
        </Tag>
      );
    },
  },
  {
    title: "創建時間",
    dataIndex: "createdAt",
    key: "createdAt",
    width: 150,
    render: (date: string) => dayjs(date).format("YYYY-MM-DD HH:mm"),
    sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
  },
  {
    title: "分配給",
    dataIndex: "user",
    key: "user",
    width: 150,
    render: (user: CouponRecord["user"]) => {
      if (!user) return "-";
      return user.email || user.id || "未設定";
    },
  },
];

export const CouponManagePage: React.FC = () => {
  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchText]);

  // React Query for fetching coupons
  const queryParams = useMemo(
    () => ({
      page: currentPage,
      pageSize,
      search: debouncedSearchText,
      status: statusFilter === "all" ? undefined : statusFilter,
    }),
    [currentPage, pageSize, debouncedSearchText, statusFilter],
  );

  const { data: couponsData, isLoading } = useCoupons(queryParams);
  const createCouponsMutation = useCreateCoupons();
  const refreshCoupons = useRefreshCoupons();

  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleAddCoupons = (values: { coupons: string }) => {
    const couponCodes = values.coupons
      .split(/[\n,]/)
      .map((code) => code.trim())
      .filter((code) => code.length > 0);

    if (couponCodes.length === 0) {
      return;
    }

    createCouponsMutation.mutate(
      { coupons: couponCodes },
      {
        onSuccess: () => {
          setIsModalVisible(false);
          form.resetFields();
        },
      },
    );
  };

  const exportToCSV = () => {
    if (!couponsData?.data) return;

    const csvContent = [
      ["獎券代碼", "狀態", "創建時間", "分配給"],
      ...couponsData.data.map((record: CouponRecord) => [
        record.code,
        record.status === "available" ? "可用" : "已分配",
        dayjs(record.createdAt).format("YYYY-MM-DD HH:mm:ss"),
        record.user?.email || record.user?.id || "-",
      ]),
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `coupons-${dayjs().format("YYYY-MM-DD")}.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">PIN 碼獎券管理</h2>

      {/* Statistics Cards */}
      <div className="mb-4">
        <Row gutter={16} className="mb-4">
          <Col span={6}>
            <Card>
              <Statistic
                title="總獎券數"
                value={couponsData?.stats.total || 0}
                valueStyle={{ color: "#1677ff" }}
                prefix={<GiftOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="可用獎券"
                value={couponsData?.stats.available || 0}
                valueStyle={{ color: "#52c41a" }}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已分配"
                value={couponsData?.stats.assigned || 0}
                valueStyle={{ color: "#fa8c16" }}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
        </Row>
      </div>

      {/* Controls */}
      <Space className="mb-4" wrap>
        <Search
          placeholder="搜尋獎券代碼或用戶"
          allowClear
          enterButton={<SearchOutlined />}
          size="middle"
          onSearch={handleSearch}
          style={{ width: 300 }}
        />

        <Select
          value={statusFilter}
          onChange={handleStatusFilter}
          style={{ width: 120 }}
        >
          <Option value="all">全部狀態</Option>
          <Option value="available">可用</Option>
          <Option value="assigned">已分配</Option>
        </Select>

        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsModalVisible(true)}
        >
          新增獎券
        </Button>

        <Button
          type="primary"
          onClick={() => {
            refreshCoupons();
            setCurrentPage(1);
            setSearchText("");
            setStatusFilter("all");
          }}
          loading={isLoading}
        >
          重新整理
        </Button>

        <Button
          icon={<DownloadOutlined />}
          onClick={exportToCSV}
          disabled={!couponsData?.data || couponsData.data.length === 0}
        >
          匯出CSV
        </Button>
      </Space>

      {/* Table */}
      <Table
        columns={columns}
        dataSource={couponsData?.data || []}
        loading={isLoading}
        rowKey="id"
        scroll={{ x: 600 }}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: couponsData?.pagination.total || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 筆記錄`,
          onChange: (page, size) => {
            setCurrentPage(page);
            if (size !== pageSize) {
              setPageSize(size);
              setCurrentPage(1);
            }
          },
          onShowSizeChange: (_, size) => {
            setPageSize(size);
            setCurrentPage(1);
          },
        }}
      />

      {/* Add Coupons Modal */}
      <Modal
        title="批量新增獎券"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleAddCoupons}>
          <Form.Item
            name="coupons"
            label="獎券代碼"
            rules={[{ required: true, message: "請輸入獎券代碼" }]}
            extra="每行一個代碼，或用逗號分隔。系統會自動去除重複和無效的代碼。"
          >
            <TextArea
              rows={10}
              placeholder="請輸入獎券代碼，每行一個或用逗號分隔&#10;例如：&#10;COUPON001&#10;COUPON002&#10;COUPON003"
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                新增獎券
              </Button>
              <Button
                onClick={() => {
                  setIsModalVisible(false);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
