import { auth } from "@/libs/auth";
import { notFound } from "next/navigation";
import { PropsWithChildren } from "react";
import "./index.css";
import { LineLogin } from "../components/login-buttons";

export default async function Layout({ children }: PropsWithChildren) {
  const session = await auth();

  const userRole = session?.user.systemRole;

  if (!session?.user) {
    return (
      <div className="h-screen w-screen grid  place-items-center">
        <div className="w-[200px]">
          <LineLogin />
        </div>
      </div>
    );
  }

  if (userRole !== "ADMIN") {
    return notFound();
  }

  return <>{children}</>;
}
