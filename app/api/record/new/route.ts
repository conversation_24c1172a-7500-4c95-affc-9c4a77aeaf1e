import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

// Game-specific score limits and time constraints
const GAME_LIMITS = {
  balance: {
    maxScore: 60,
    minTime: 100,
    maxTime: 65000,
  },
  catch: {
    maxScore: 5000,
    minTime: 55000,
    maxTime: 65000,
  },
  quiz: {
    maxScore: 100,
    minTime: 1000,
    maxTime: 65000,
  },
} as const;

export async function POST(req: NextRequest) {
  const session = await auth();
  const userId = session?.user.id;
  const banned = session?.user.banned;

  if (!userId) {
    return NextResponse.json({ message: "請先登入" }, { status: 401 });
  }
  if (banned) {
    return NextResponse.json({ message: "帳號已被限制" }, { status: 403 });
  }

  try {
    const body = await req.json();
    const { gameId, score } = body;

    // Validate required fields
    if (!gameId || score === undefined) {
      return NextResponse.json({ message: "缺少必要參數" }, { status: 400 });
    }

    // Validate game ID
    if (!GAME_LIMITS[gameId as keyof typeof GAME_LIMITS]) {
      return NextResponse.json({ message: "無效的遊戲ID" }, { status: 400 });
    }

    const limits = GAME_LIMITS[gameId as keyof typeof GAME_LIMITS];

    // Validate play count
    const recordCount = await prisma.gameRecord.count({
      where: { userId, gameId },
    });

    const playCount =
      (await prisma.playCount.findFirst({ where: { userId, gameId } }))
        ?.count ?? 0;

    if (playCount + 10 < recordCount) {
      await prisma.suspiciousActivity.create({
        data: {
          userId,
          gameId,
          reason: `遊戲次數不匹配 playCount:${playCount} recordCount:${recordCount}`,
        },
      });

      // ban user
      await prisma.user.update({
        where: { id: userId },
        data: { banned: true },
      });

      return NextResponse.json({ message: "遊戲次數不匹配" }, { status: 400 });
    }

    // Validate score limits
    if (score < 0 || score > limits.maxScore) {
      // Log suspicious score
      console.warn(`Suspicious score submitted by user ${userId}`, {
        gameId,
        score,
        maxAllowed: limits.maxScore,
        timestamp: new Date().toISOString(),
      });

      const getEventPrefix = () => {
        switch (gameId) {
          case "balance":
            return "第一關";
          case "catch":
            return "第二關";
          case "quiz":
            return "第三關";
          default:
        }
      };

      // Record suspicious activity
      await prisma.suspiciousActivity.create({
        data: {
          userId,
          gameId,
          reason: `${getEventPrefix()}分數超出合理範圍 score:${score}`,
        },
      });

      // ban the user
      await prisma.user.update({
        where: { id: userId },
        data: { banned: true },
      });

      return NextResponse.json(
        { message: "分數超出合理範圍" },
        { status: 400 },
      );
    }

    // Create the game record
    const gameRecord = await prisma.gameRecord.create({
      data: {
        gameId,
        userId,
        score,
      },
    });

    return NextResponse.json({
      id: gameRecord.id,
      message: "記錄創建成功",
    });
  } catch (error) {
    console.error("Error creating game record:", error);
    return NextResponse.json(
      {
        message: "伺服器錯誤，請稍後再試",
      },
      { status: 500 },
    );
  }
}
