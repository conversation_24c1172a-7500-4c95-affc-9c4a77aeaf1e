import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { verifyRecaptcha } from "@/libs/recaptcha";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return NextResponse.json({ message: "請先登入" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const {
      luckyDrawName,
      luckyDrawAge,
      luckyDrawPhone,
      luckyDrawAddress,
      recaptchaToken,
    } = body;

    // Verify reCAPTCHA token first
    if (!recaptchaToken) {
      return NextResponse.json({ message: "請完成人機驗證" }, { status: 400 });
    }

    const isRecaptchaValid = await verifyRecaptcha(recaptchaToken);
    if (!isRecaptchaValid) {
      return NextResponse.json(
        { message: "人機驗證失敗，請重試" },
        { status: 400 },
      );
    }

    // Validate required fields
    if (
      !luckyDrawName ||
      !luckyDrawAge ||
      !luckyDrawPhone ||
      !luckyDrawAddress
    ) {
      return NextResponse.json(
        { message: "請填寫所有必要欄位" },
        { status: 400 },
      );
    }

    // Validate age is a positive number
    const age = parseInt(luckyDrawAge);
    if (isNaN(age) || age <= 0 || age > 150) {
      return NextResponse.json(
        { message: "請輸入有效的年齡" },
        { status: 400 },
      );
    }

    // Validate phone number (basic validation)
    const phoneRegex = /^[\d\-\+\(\)\s]+$/;
    if (!phoneRegex.test(luckyDrawPhone)) {
      return NextResponse.json(
        { message: "請輸入有效的電話號碼" },
        { status: 400 },
      );
    }

    // Update user's lucky draw information
    await prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        luckyDrawName: luckyDrawName.trim(),
        luckyDrawAge: age,
        luckyDrawPhone: luckyDrawPhone.trim(),
        luckyDrawAddress: luckyDrawAddress.trim(),
      },
    });

    return NextResponse.json({
      message: "抽獎資料提交成功",
      success: true,
    });
  } catch (error) {
    console.error("Prize form submission error:", error);
    return NextResponse.json(
      { message: "伺服器錯誤，請稍後再試" },
      { status: 500 },
    );
  }
}
