import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET() {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  try {
    // Get the first (and should be only) website config
    const config = await prisma.websiteConfig.findFirst({
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!config) {
      // Create default config if none exists
      const defaultConfig = await prisma.websiteConfig.create({
        data: {
          name: "威金森超激夏祭活動",
          startDate: new Date("2025-07-07T10:00:00.000Z"),
          endDate: new Date("2025-08-10T23:59:59.999Z"),
          luckyDrawChance: 0.1, // 10% default chance
        },
      });
      return NextResponse.json(defaultConfig);
    }

    return NextResponse.json(config);
  } catch (error) {
    console.error("Error fetching website config:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  try {
    const body = await req.json();
    const {
      name,
      startDate,
      endDate,
      luckyDrawChance,
      revealDate,
      prizeOwnerName,
      prizeOwnerPhone,
    } = body;

    // Validate required fields
    if (!name || !startDate || !endDate || luckyDrawChance === undefined) {
      return NextResponse.json({ message: "缺少必要參數" }, { status: 400 });
    }

    // Validate lucky draw chance is between 0 and 1
    if (luckyDrawChance < 0 || luckyDrawChance > 1) {
      return NextResponse.json(
        { message: "中獎機率必須在 0-1 之間" },
        { status: 400 },
      );
    }

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    if (start >= end) {
      return NextResponse.json(
        { message: "開始時間必須早於結束時間" },
        { status: 400 },
      );
    }

    // Get existing config
    const existingConfig = await prisma.websiteConfig.findFirst({
      orderBy: {
        createdAt: "desc",
      },
    });

    let updatedConfig;

    if (existingConfig) {
      // Update existing config
      updatedConfig = await prisma.websiteConfig.update({
        where: {
          id: existingConfig.id,
        },
        data: {
          name,
          startDate: start,
          endDate: end,
          luckyDrawChance,
          revealDate: revealDate ? new Date(revealDate) : null,
          prizeOwnerName: prizeOwnerName || null,
          prizeOwnerPhone: prizeOwnerPhone || null,
        },
      });
    } else {
      // Create new config if none exists
      updatedConfig = await prisma.websiteConfig.create({
        data: {
          name,
          startDate: start,
          endDate: end,
          luckyDrawChance,
          revealDate: revealDate ? new Date(revealDate) : null,
          prizeOwnerName: prizeOwnerName || null,
          prizeOwnerPhone: prizeOwnerPhone || null,
        },
      });
    }

    return NextResponse.json(updatedConfig);
  } catch (error) {
    console.error("Error updating website config:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
