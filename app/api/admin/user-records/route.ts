import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { Prisma } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  const page = parseInt(req.nextUrl.searchParams.get("page") || "1");
  const pageSize = parseInt(req.nextUrl.searchParams.get("pageSize") || "20");
  const search = req.nextUrl.searchParams.get("search") || "";

  try {
    // Build where clause for search
    const whereClause: Prisma.UserFindManyArgs["where"] = {};

    if (search) {
      whereClause.OR = [
        {
          nickname: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          email: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          luckyDrawName: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          luckyDrawPhone: {
            contains: search,
          },
        },
      ];
    }

    // Get total count for pagination
    const totalCount = await prisma.user.count({
      where: whereClause,
    });

    // Calculate pagination
    const skip = (page - 1) * pageSize;
    const totalPages = Math.ceil(totalCount / pageSize);

    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        nickname: true,
        email: true,
        luckyDrawName: true,
        luckyDrawAge: true,
        luckyDrawAddress: true,
        luckyDrawPhone: true,
        createdAt: true,
        GameRecord: {
          select: {
            gameId: true,
            score: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        Coupon: {
          select: {
            code: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: pageSize,
    });

    const formattedUsers = users.map((user) => ({
      id: user.id,
      nickname: user.nickname,
      email: user.email,
      luckyDrawName: user.luckyDrawName,
      luckyDrawAge: user.luckyDrawAge,
      luckyDrawAddress: user.luckyDrawAddress,
      luckyDrawPhone: user.luckyDrawPhone,
      createdAt: user.createdAt.toISOString(),
      gameRecords: user.GameRecord.map((record) => ({
        gameId: record.gameId,
        score: Number(record.score),
        createdAt: record.createdAt.toISOString(),
      })),
      coupons: user.Coupon.map((coupon) => ({
        code: coupon.code,
        createdAt: coupon.createdAt.toISOString(),
      })),
    }));

    return NextResponse.json({
      data: formattedUsers,
      pagination: {
        current: page,
        pageSize,
        total: totalCount,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Error fetching user records:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
