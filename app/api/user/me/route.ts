import { auth } from "@/libs/auth";
import { NextResponse } from "next/server";

export async function GET() {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return NextResponse.json({ message: "請先登入" }, { status: 401 });
  }

  const {
    id,
    email,
    image,
    name,
    nickname,
    luckyDraw<PERSON><PERSON>,
    luckyDrawAge,
    luckyDrawAddress,
    luckyDrawPhone,
  } = session.user;

  try {
    return NextResponse.json({
      id,
      email,
      image,
      name,
      nickname,
      luckyDrawName,
      luckyDrawAge,
      luckyDrawAddress,
      luckyDrawPhone,
    });
  } catch (error) {
    console.error("Error updating nickname:", error);
    return NextResponse.json(
      { message: "伺服器錯誤，請稍後再試" },
      { status: 500 },
    );
  }
}
