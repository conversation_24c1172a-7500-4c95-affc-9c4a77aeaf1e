import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return NextResponse.json({ message: "請先登入" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { nickname } = body;

    // Validate required fields
    if (!nickname || typeof nickname !== "string") {
      return NextResponse.json(
        { message: "請提供有效的暱稱" },
        { status: 400 },
      );
    }

    // Validate nickname length and content
    const trimmedNickname = nickname.trim();
    if (trimmedNickname.length === 0) {
      return NextResponse.json({ message: "暱稱不能為空" }, { status: 400 });
    }

    if (trimmedNickname.length > 30) {
      return NextResponse.json(
        { message: "暱稱長度不能超過30個字符" },
        { status: 400 },
      );
    }

    // Update user's nickname
    const updatedUser = await prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        nickname: trimmedNickname,
      },
      select: {
        id: true,
        nickname: true,
      },
    });

    return NextResponse.json({
      message: "暱稱更新成功",
      nickname: updatedUser.nickname,
      success: true,
    });
  } catch (error) {
    console.error("Error updating nickname:", error);
    return NextResponse.json(
      {
        message: "伺服器錯誤，請稍後再試",
      },
      { status: 500 },
    );
  }
}
