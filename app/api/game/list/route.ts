import { auth } from "@/libs/auth";
import { query } from "./query";
import { NextResponse } from "next/server";

export async function GET() {
  const session = await auth();
  const userId = session?.user.id;
  if (!userId) {
    return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
  }

  const hasCompletePrizeForm =
    !!session.user.luckyDrawName &&
    !!session.user.luckyDrawAge &&
    !!session.user.luckyDrawPhone &&
    !!session.user.luckyDrawAddress;

  const gameList = await query(userId);

  return Response.json({ gameList: gameList, hasCompletePrizeForm });
}
