import { auth } from "@/libs/auth";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return NextResponse.json({ message: "請先登入" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { gameId } = body;

    if (!gameId) {
      return NextResponse.json({ message: "缺少遊戲ID" }, { status: 400 });
    }

    // Validate game ID
    const validGameIds = ["balance", "catch", "quiz"];
    if (!validGameIds.includes(gameId)) {
      return NextResponse.json({ message: "無效的遊戲ID" }, { status: 400 });
    }

    return NextResponse.json({
      expiresAt: Date.now() + 300000, // 5 minutes
    });
  } catch (error) {
    console.error("Error creating game session:", error);
    return NextResponse.json(
      { message: "伺服器錯誤，請稍後再試" },
      { status: 500 },
    );
  }
}
