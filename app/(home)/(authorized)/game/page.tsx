"use client";
import type { GameList } from "@/app/api/game/list/query";
import { GameId } from "@/app/constants";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useCallback, useState, useEffect } from "react";
import { GameState } from "./game-entries";
import { GameResult } from "./game-result";
import {
  INIT_LOADING_STATE,
  useImagePreloader,
} from "../../../hooks/use-image-preloader";
import { GameTitle } from "./components";
import {
  LoadingStage,
  NicknameStage,
  EntranceStage,
  InstructionStage,
  PlayStage,
} from "./stages";

enum Stage {
  Loading = "loading",
  Nickname = "nickname",
  Entrance = "entrance",
  Instruction = "Instruction",
  Play = "play",
  Result = "Result",
}

const formatDate = (date: Date) => {
  // x月x日
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${month}月${day}日`;
};

const GamePage = () => {
  const [nickname, setNickname] = useState<string>(
    INIT_LOADING_STATE.nickname ?? "",
  );

  const [stage, setStage] = useState<Stage>(
    INIT_LOADING_STATE.finished
      ? INIT_LOADING_STATE.nickname
        ? Stage.Entrance
        : Stage.Nickname
      : Stage.Loading,
  );
  const [gameId, setGameId] = useState<GameId>("balance");
  const [score, setScore] = useState(0);
  const [gameRecordId, setGameRecordId] = useState<string>("");

  const { data: userData } = useQuery({
    queryKey: ["user"],
    queryFn: async () => {
      const response = await fetch("/api/user/me");
      if (response.ok) {
        const data = await response.json();
        INIT_LOADING_STATE.nickname = data.nickname;
        return data;
      }
      return null;
    },
  });

  const { progress, isComplete, preloadImages } = useImagePreloader();

  // Start preloading images when component mounts
  useEffect(() => {
    preloadImages();
  }, [preloadImages]);

  // Move to nickname stage when preloading is complete
  useEffect(() => {
    if (isComplete && userData && stage === Stage.Loading) {
      if (userData.nickname) {
        setNickname(userData.nickname);
        setStage(Stage.Entrance);
      } else {
        setStage(Stage.Nickname);
      }
    }
  }, [isComplete, stage, userData]);

  const { data: gameList } = useQuery({
    queryFn: async (): Promise<GameState> => {
      const res: { gameList: GameList } = await fetch("/api/game/list").then(
        (r) => r.json(),
      );

      const state: GameState = {};

      res?.gameList?.forEach((item) => {
        const { name, startTime, GameRecord } = item;
        state[name] = {
          available: new Date(startTime).getTime() <= Date.now(),
          startDate: formatDate(new Date(startTime)),
          played: !!GameRecord.length,
        } satisfies GameState[number];
      });

      return state;
    },
    queryKey: ["game", "list"],
  });

  const { mutate: updateNickname } = useMutation({
    mutationFn: async () => {
      const response = await fetch("/api/user/nickname", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ nickname }),
      });

      if (response.ok) {
        return response.json();
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to update nickname");
      }
    },
    onSuccess: () => setStage(Stage.Entrance),
    onError: (error) => {
      console.error("Error updating nickname:", error);
      alert(error.message || "更新暱稱失敗，請重試");
    },
  });

  const { mutate: createRecord } = useMutation({
    mutationFn: async ({ score }: { score: number }) => {
      const response = await fetch("/api/record/new", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ score, gameId }),
      });

      if (response.ok) {
        const result = await response.json();
        return result;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to create record");
      }
    },
    onSuccess: (data) => {
      if (data?.id) {
        setGameRecordId(data.id);
      }
    },
    onError: (error) => {
      console.error("Error creating record:", error);
      alert(error.message || "提交分數失敗，請重試");
    },
  });

  const { mutate: incrementPlayCount } = useMutation({
    mutationFn: async () => {
      const response = await fetch("/api/play/start", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ gameId }),
      });

      if (response.ok) {
        const result = await response.json();
        return result;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to increment play count");
      }
    },
    onError: (error) => {
      console.error("Error incrementing play count:", error);
      // Don't show alert for play count errors as it's not critical for gameplay
    },
  });

  const onGameEnd = useCallback(
    async (score: number) => {
      // For now, we'll skip reCAPTCHA in the game end flow
      // and add it to the draw process instead
      try {
        createRecord({
          score,
        });
      } catch (error) {
        console.error("Failed to submit score:", error);
      } finally {
        setScore(score);
        setTimeout(() => setStage(Stage.Result), 1000);
      }
    },
    [createRecord],
  );

  if (stage === Stage.Loading) {
    return <LoadingStage progress={progress} />;
  }

  if (stage === Stage.Nickname) {
    if (!userData) {
      return null;
    }
    return (
      <NicknameStage
        nickname={nickname}
        onNicknameChange={setNickname}
        onSubmit={updateNickname}
      />
    );
  }

  if (stage === Stage.Entrance) {
    return (
      <EntranceStage
        gameState={gameList}
        onGameSelect={(gameId) => {
          setGameId(gameId);
          setStage(Stage.Instruction);
        }}
      />
    );
  }

  if (stage === Stage.Instruction) {
    return (
      <InstructionStage
        gameId={gameId}
        onStart={() => {
          // Increment play count when user starts the game
          incrementPlayCount();
          setStage(Stage.Play);
        }}
      />
    );
  }

  if (stage === Stage.Play) {
    return <PlayStage gameId={gameId} onGameEnd={onGameEnd} />;
  }

  if (stage === Stage.Result) {
    return (
      <GameResult
        gameTitle={<GameTitle gameId={gameId} />}
        nickname={nickname}
        gameId={gameId}
        score={score}
        gameRecordId={gameRecordId}
        onPlayAgain={() => {
          setGameRecordId("");
          setStage(Stage.Instruction);
        }}
      />
    );
  }
};

export default GamePage;
