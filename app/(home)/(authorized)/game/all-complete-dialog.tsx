"use client";
import { DialogButton } from "@/app/components/buttons/dialog-button";
import { DialogBase } from "@/app/components/dialog-base";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";
import { useEffect, useState } from "react";
import Link from "next/link";
import { ctaClickEvent } from "@/utils/ga-event";

export const AllCompleteDialog = () => {
  const [visible, setVisible] = useState(false);

  const onClose = () => {
    setVisible(false);
  };

  useEffect(() => {
    setVisible(true);
  }, []);

  if (!visible) {
    return null;
  }

  return (
    <DialogBase visible={visible}>
      <div className="w-[79vw] aspect-[851/1262] relative z-0 flex flex-col items-center text-center pt-[12vw]">
        <Image
          unoptimized
          priority
          src={imageUrl("/popup-game-complete-all.png")}
          alt=""
          className="absolute -z-10 top-0 left-0 w-full h-full ml-[1.3vw]"
          unselectable="on"
          width={851}
          height={1262}
        />
        <div className="text-[4.3vw] font-[1000] mt-[22vw] mb-[3vw]">
          恭喜您完成所有挑戰
        </div>

        <div className="text-[6.4vw] leading-[7.5vw] text-[#fff100] font-[1000] mt-[4vw] mb-[1vw]">
          現在登入資料
          <br />
          就有機會抽中PS5 PRO
        </div>

        <div className="mt-[3.5vw]">
          <Link
            href="/prize-form"
            onClick={() => {
              ctaClickEvent({ label: "遊戲結束_登入資料" });
            }}
          >
            <DialogButton as="div">登入資料</DialogButton>
          </Link>
        </div>

        <button
          className="absolute top-0 right-0 w-[9vw] h-[9vw]"
          onClick={onClose}
        />
      </div>
    </DialogBase>
  );
};
