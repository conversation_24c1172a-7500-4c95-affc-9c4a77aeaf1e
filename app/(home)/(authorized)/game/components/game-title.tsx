import { GameId } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import clsx from "clsx";
import Image from "next/image";

interface GameTitleProps {
  gameId: GameId;
}

export const GameTitle = ({ gameId }: GameTitleProps) => {
  const image = {
    balance: "/game-title-balance.png",
    catch: "/game-title-catch.png",
    quiz: "/game-title-quiz.png",
  }[gameId];

  const offset = {
    balance: "pt-[5vw]",
    catch: "pt-[2vw]",
    quiz: "pb-[1vw]",
  }[gameId];

  return (
    <Image
      unoptimized
      priority
      alt=""
      className={clsx("w-full h-auto", offset)}
      src={imageUrl(image)}
      width={786}
      height={258}
    />
  );
};
