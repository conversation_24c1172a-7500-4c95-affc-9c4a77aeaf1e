import clsx from "clsx";
import { useState } from "react";

export const GameStartWipe = () => {
  const [isFadeOut, setISFadeOut] = useState(false);
  const [isEnded, setIsEnded] = useState(false);

  if (isEnded) {
    return null;
  }

  return (
    <div
      className={clsx(
        "absolute top-0 left-0 left-[5vw] top-[20vw] w-[90vw] h-[155vw]",
        "user-select-none pointer-events-none",
        "overflow-hidden bg-black transition-all",
        { "opacity-0": isFadeOut },
      )}
    >
      <video
        controls={false}
        onPlay={() => {
          setTimeout(() => {
            setISFadeOut(true);
          }, 1000);
          setTimeout(() => {
            setIsEnded(true);
          }, 2000);
        }}
        className={"w-full h-2/3 object-cover absolute left-0 bottom-0"}
        src="/remv_0703.mp4"
        playsInline
        autoPlay
      />
    </div>
  );
};
