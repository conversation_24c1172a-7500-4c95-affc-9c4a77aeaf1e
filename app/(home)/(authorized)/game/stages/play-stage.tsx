import { FullScreenImage } from "@/app/components/full-screen";
import { GameId } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import { BalanceGameApp } from "../game-app/balance";
import { CatchGameApp } from "../game-app/catch";
import { QuizGameApp } from "../game-app/quiz";
import { GameTitle } from "../components/game-title";
import { useCallback } from "react";
import { GameStartWipe } from "./game-start-wipe";

interface PlayStageProps {
  gameId: GameId;
  onGameEnd: (score: number) => void;
}

export const PlayStage = ({ gameId, onGameEnd }: PlayStageProps) => {
  const handleGameEnd = useCallback(
    (score: number) => {
      onGameEnd(score);
    },
    [onGameEnd],
  );
  const renderGameApp = () => {
    switch (gameId) {
      case "balance":
        return <BalanceGameApp onGameEnd={handleGameEnd} />;
      case "catch":
        return <CatchGameApp onGameEnd={handleGameEnd} />;
      case "quiz":
        return <QuizGameApp onGameEnd={handleGameEnd} />;
      default:
        return (
          <div className="text-center text-[4vw] font-bold">Game Not Found</div>
        );
    }
  };

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-game.png")} />
      <div className="relative z-0 flex flex-col items-center pt-[4vw]">
        <div className="w-[73vw] h-[23.5vw] flex justify-center items-center">
          <GameTitle gameId={gameId} />
        </div>
        <div>{renderGameApp()}</div>
      </div>
      <GameStartWipe />
    </>
  );
};
