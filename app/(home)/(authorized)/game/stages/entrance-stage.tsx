import { FullScreenImage } from "@/app/components/full-screen";
import { GameId } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import { GameEntries, GameState } from "../game-entries";

interface EntranceStageProps {
  gameState: GameState | undefined;
  onGameSelect: (gameId: GameId) => void;
}

export const EntranceStage = ({
  gameState,
  onGameSelect,
}: EntranceStageProps) => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-game-entries.png")} />
      <div className="relative z-0 flex flex-col items-center pt-[38vw]">
        <GameEntries onEntryClick={onGameSelect} gameState={gameState} />
      </div>
      <video
        preload="auto"
        controls={false}
        src="/remv_0703.mp4"
        className="absolute hidden"
        autoPlay={false}
      />
    </>
  );
};
