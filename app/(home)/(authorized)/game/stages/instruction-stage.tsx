import { GameButton } from "@/app/components/buttons/game-button";
import { FullScreenImage } from "@/app/components/full-screen";
import { GameId } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import { GameInstructions } from "../components/game-instructions";
import { GameTitle } from "../components/game-title";

interface InstructionStageProps {
  gameId: GameId;
  onStart: () => void;
}

export const InstructionStage = ({
  gameId,
  onStart,
}: InstructionStageProps) => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-game.png")} />
      <div className="relative z-0 flex flex-col items-center pt-[4vw]">
        <div className="w-[73vw] h-[23.5vw] flex justify-center items-center">
          <GameTitle gameId={gameId} />
        </div>

        <div
          className="w-[77vw] h-[113vw] flex flex-col items-center pt-[13vw]"
          style={{
            backgroundImage: `url(${imageUrl("/game-instruction-dialog.png")})`,
            backgroundSize: "contain",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center",
          }}
        >
          <h2 className="text-[8.7vw] font-[1000] text-[#fff100]">遊戲說明</h2>
          <p className="h-[57vw] text-center text-[4vw] leading-[2.1] font-bold">
            <GameInstructions gameId={gameId} />
          </p>
          <GameButton onClick={onStart}>START</GameButton>
        </div>
      </div>
    </>
  );
};
