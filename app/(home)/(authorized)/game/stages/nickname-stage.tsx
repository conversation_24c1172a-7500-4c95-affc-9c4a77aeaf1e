import { AppButton } from "@/app/components/buttons/app-button";
import { FullScreenImage } from "@/app/components/full-screen";
import { TexturedText } from "@/app/components/textured-text";
import { imageUrl } from "@/utils/image-url";
import style from "../index.module.scss";
import { ctaClickEvent } from "@/utils/ga-event";

interface NicknameStageProps {
  nickname: string;
  onNicknameChange: (nickname: string) => void;
  onSubmit: () => void;
}

export const NicknameStage = ({
  nickname,
  onNicknameChange,
  onSubmit,
}: NicknameStageProps) => {
  const handleSubmit = (e: React.FormEvent) => {
    ctaClickEvent({ label: "開始遊戲" });
    e.preventDefault();
    if (nickname) {
      onSubmit();
    } else {
      alert("請輸入暱稱");
    }
  };

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-login.png")} />
      <form
        className="flex flex-col items-center relative pt-[56.5vw]"
        onSubmit={handleSubmit}
      >
        <TexturedText className="font-[1000] text-[5.5vw]">
          請輸入您的暱稱
        </TexturedText>

        <input
          name="nickname"
          className={style.input}
          value={nickname}
          maxLength={30}
          onChange={(e) => onNicknameChange(e.target.value)}
        />

        <AppButton disabled={!nickname}>開始遊戲</AppButton>
      </form>
    </>
  );
};
