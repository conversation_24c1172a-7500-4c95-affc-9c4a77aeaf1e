import clsx from "clsx";

export const GameDisplay = ({
  title,
  value,
  className,
}: {
  title: string;
  value: number | string;
  className?: string;
}) => {
  return (
    <div className={clsx(className, "flex gap-[2vw] items-center")}>
      <span className="text-[3.5vw] leading-[4vw] text-[#fff100] font-[1000] border rounded-[1vw] px-[0.7vw] mb-[1vw]">
        {title}
      </span>
      <span
        className="text-[5.5vw] font-bold"
        style={{ fontFamily: "MStiffHeiHK-UltraBold" }}
      >
        {value}
      </span>
    </div>
  );
};
