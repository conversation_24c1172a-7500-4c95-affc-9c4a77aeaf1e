export class Vec2 {
  x: number;
  y: number;

  constructor(x: number, y: number) {
    this.x = x;
    this.y = y;
  }

  add(other: Vec2) {
    return new Vec2(this.x + other.x, this.y + other.y);
  }

  sub(other: Vec2) {
    return new Vec2(this.x - other.x, this.y - other.y);
  }

  mul(scalar: number) {
    return new Vec2(this.x * scalar, this.y * scalar);
  }

  normalize() {
    const length = Math.sqrt(this.x * this.x + this.y * this.y);
    return new Vec2(this.x / length, this.y / length);
  }
}
