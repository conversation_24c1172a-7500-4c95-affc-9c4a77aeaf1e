import { useEffect, useMemo, useState } from "react";
import { QuizGame, QuizGameState } from "./quiz-game";
import { QuizGameView } from "./quiz-game-view";

export const QuizGameApp = ({
  onGameEnd,
}: {
  onGameEnd: (score: number) => void;
}) => {
  const [data, setData] = useState<QuizGameState>();

  const gameApp = useMemo(() => {
    return new QuizGame({
      onUpdateUi: setData,
      onGameEnd: onGameEnd,
    });
  }, [onGameEnd]);

  useEffect(() => {
    setTimeout(() => {
      gameApp.start();
    }, 1000);
  }, [gameApp]);

  return (
    <div className="w-full h-full">
      <QuizGameView gameState={data} onAnswerSelect={gameApp.onAnswerSelect} />
    </div>
  );
};
