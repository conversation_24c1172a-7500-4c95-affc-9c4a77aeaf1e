export interface QuizQuestion {
  id: number;
  question: string;
  options: string[];
  correctAnswer: number; // Index of correct option
  timeLimit: number; // in milliseconds
}

export interface QuizGameState {
  passedTime: number;
  score: number;
  currentQuestionIndex: number;
  currentQuestion: QuizQuestion | null;
  timeRemaining: number;
  isAnswered: boolean;
  selectedAnswer: number | null;
  isCorrect: boolean | null;
  gamePhase: "playing" | "showResult" | "finished";
  totalQuestions: number;
  correctAnswers: number;
  wrongAnswers: number;
}

const QUIZ_QUESTIONS: QuizQuestion[] = [
  {
    id: 1,
    question: "威金森品牌有幾年歷史？",
    options: ["1904年", "100年", "121年"],
    correctAnswer: 2, // 121年
    timeLimit: 10000,
  },
  {
    id: 2,
    question: "威金森創辦人\n在哪個國家創立此品牌？",
    options: ["英國", "日本", "挪威"],
    correctAnswer: 1, // 日本
    timeLimit: 10000,
  },
  {
    id: 3,
    question: "以下何者\n不是威金森的包裝形式？",
    options: ["寶特瓶", "鋁罐", "24K金"],
    correctAnswer: 2, // 24K金
    timeLimit: 10000,
  },
  {
    id: 4,
    question: "以下何者\n是威金森碳酸水品牌特色？",
    options: ["氣泡刺激強烈", "百年歷史", "日本暢銷No.1", "以上皆是"],
    correctAnswer: 3, // 以上皆是
    timeLimit: 10000,
  },
  {
    id: 5,
    question: "威金森碳酸水的正確喝法？",
    options: ["直接大口暢飲", "做成調飲喝", "吃完飯解膩喝", "以上皆是"],
    correctAnswer: 3, // 以上皆是
    timeLimit: 10000,
  },
];

export class QuizGame {
  startTime = Date.now();
  animationRequestId = 0;
  passedTime = 0;
  score = 0;
  currentQuestionIndex = 0;
  timeRemaining = 0;
  isAnswered = false;
  selectedAnswer: number | null = null;
  isCorrect: boolean | null = null;
  gamePhase: "playing" | "showResult" | "finished" = "playing";
  correctAnswers = 0;
  wrongAnswers = 0;
  questionStartTime = 0;

  onUpdateUi: (state: QuizGameState) => void;
  onGameEnd: (score: number) => void;

  constructor({
    onUpdateUi,
    onGameEnd,
  }: {
    onUpdateUi: (state: QuizGameState) => void;
    onGameEnd: (score: number) => void;
  }) {
    this.onUpdateUi = onUpdateUi;
    this.onGameEnd = onGameEnd;
  }

  start = () => {
    this.startTime = Date.now();
    this.currentQuestionIndex = 0;
    this.score = 0;
    this.correctAnswers = 0;
    this.wrongAnswers = 0;
    this.startQuestion();
    this.animationRequestId = requestAnimationFrame(this.update);
  };

  startQuestion = () => {
    this.questionStartTime = Date.now();
    this.timeRemaining = QUIZ_QUESTIONS[this.currentQuestionIndex].timeLimit;
    this.isAnswered = false;
    this.selectedAnswer = null;
    this.isCorrect = null;
    this.gamePhase = "playing";
  };

  onAnswerSelect = (answerIndex: number) => {
    if (this.isAnswered || this.gamePhase !== "playing") return;

    this.isAnswered = true;
    this.selectedAnswer = answerIndex;
    const currentQuestion = QUIZ_QUESTIONS[this.currentQuestionIndex];
    this.isCorrect = answerIndex === currentQuestion.correctAnswer;

    // Update score
    if (this.isCorrect) {
      this.score += 20;
      this.correctAnswers++;
    } else {
      this.score = Math.max(0, this.score - 10); // Don't go below 0
      this.wrongAnswers++;
    }

    this.gamePhase = "showResult";

    // Show result for 2 seconds, then move to next question
    setTimeout(() => {
      this.nextQuestion();
    }, 1000);
  };

  nextQuestion = () => {
    this.currentQuestionIndex++;

    if (this.currentQuestionIndex >= QUIZ_QUESTIONS.length) {
      this.endGame();
    } else {
      this.startQuestion();
    }
  };

  onTimeUp = () => {
    if (this.isAnswered) return;

    this.isAnswered = true;
    this.selectedAnswer = null;
    this.isCorrect = false;
    this.score = Math.max(0, this.score - 10); // Don't go below 0
    this.wrongAnswers++;
    this.gamePhase = "showResult";

    // Show result for 2 seconds, then move to next question
    setTimeout(() => {
      this.nextQuestion();
    }, 1000);
  };

  endGame = () => {
    this.gamePhase = "finished";
    window.cancelAnimationFrame(this.animationRequestId);
    this.onGameEnd(this.score);
  };

  update = () => {
    this.passedTime = Date.now() - this.startTime;

    if (this.gamePhase === "playing" && !this.isAnswered) {
      const questionElapsed = Date.now() - this.questionStartTime;
      const currentQuestion = QUIZ_QUESTIONS[this.currentQuestionIndex];
      this.timeRemaining = Math.max(
        0,
        currentQuestion.timeLimit - questionElapsed,
      );

      if (this.timeRemaining <= 0) {
        this.onTimeUp();
      }
    }

    this.onUpdateUi({
      passedTime: this.passedTime,
      score: this.score,
      currentQuestionIndex: this.currentQuestionIndex,
      currentQuestion: QUIZ_QUESTIONS[this.currentQuestionIndex] || null,
      timeRemaining: this.timeRemaining,
      isAnswered: this.isAnswered,
      selectedAnswer: this.selectedAnswer,
      isCorrect: this.isCorrect,
      gamePhase: this.gamePhase,
      totalQuestions: QUIZ_QUESTIONS.length,
      correctAnswers: this.correctAnswers,
      wrongAnswers: this.wrongAnswers,
    });

    if (this.gamePhase !== "finished") {
      this.animationRequestId = requestAnimationFrame(this.update);
    }
  };
}
