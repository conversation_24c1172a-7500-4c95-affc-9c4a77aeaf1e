"use client";
import Image from "next/image";
import { imageUrl } from "@/utils/image-url";
import { QuizGameState } from "./quiz-game";
import clsx from "clsx";
import { GameDisplay } from "../shared/game-display";
import { TexturedText } from "@/app/components/textured-text";
import style from "./index.module.scss";

const formatTime = (time: number) => {
  const seconds = Math.floor(time / 1000);
  const milliseconds = Math.floor((time % 1000) / 10);
  return `${seconds.toString().padStart(2, "0")}:${milliseconds.toString().padStart(2, "0")}`;
};

interface QuizGameViewProps {
  gameState: QuizGameState | undefined;
  onAnswerSelect: (answerIndex: number) => void;
}

export const QuizGameView = ({
  gameState,
  onAnswerSelect,
}: QuizGameViewProps) => {
  const {
    score = 0,
    selectedAnswer,
    currentQuestion = null,
    isCorrect,
    timeRemaining = 0,
    isAnswered = false,
    gamePhase = "playing",
  } = gameState ?? {};

  return (
    <div className="w-full flex flex-col items-center">
      {/* Score Board */}
      <div className="relative w-[70vw] h-[11vw] mt-[5.5vw]">
        <Image
          unoptimized
          alt=""
          src={imageUrl("/game-score-background.png")}
          width={756}
          height={121}
        />
        <div className="absolute top-[3.9vw] left-[3vw] flex">
          <GameDisplay title="TIME" value={formatTime(timeRemaining)} />
          <GameDisplay title="SCORE" value={score} className="ml-[6vw]" />
        </div>
      </div>

      <div className="w-[61vw] h-[98vw] flex flex-col items-center">
        {/* Question */}
        {currentQuestion && (
          <div
            className={clsx(
              "relative flex justify-center text-center my-[2.5vw]",
              "font-[1000] text-[5vw] leading-[1.2em] whitespace-pre",
            )}
          >
            <span className={style.textShadow}>{currentQuestion.question}</span>
            <div className="absolute top-0 left-0 w-full h-full flex justify-center text-center">
              <TexturedText>{currentQuestion.question}</TexturedText>
            </div>
          </div>
        )}

        {/* Answer Options */}
        {currentQuestion && (
          <div className="w-full space-y-[3vw] flex-1 flex flex-col">
            {currentQuestion.options.map((option, index) => {
              const isSelected = index === selectedAnswer;
              const backgroundImage = `url(${imageUrl(isSelected && !isCorrect ? "/game-quiz-option-result.png" : "/game-quiz-option.png")})`;

              return (
                <button
                  key={index}
                  onClick={() => onAnswerSelect(index)}
                  disabled={isAnswered || gamePhase !== "playing"}
                  className={clsx(
                    { ["saturate-0 brightness-50"]: isAnswered && !isSelected },
                    currentQuestion.options.length > 3
                      ? "text-[8vw]"
                      : "text-[12vw]",
                    "leading-[1em] font-bold text-[#fff100]",
                    "w-full p-[4vw] text-nowrap",
                    "relative z-0 flex-1 flex justify-center items-center overflow-hidden",
                  )}
                >
                  <div
                    className="absolute left-0 top-0 w-full h-1/2"
                    style={{
                      backgroundSize: "cover",
                      backgroundImage,
                      backgroundPosition: "top",
                      backgroundRepeat: "no-repeat",
                    }}
                  />
                  <div
                    className="absolute left-0 bottom-0 w-full h-1/2"
                    style={{
                      backgroundSize: "cover",
                      backgroundImage,
                      backgroundPosition: "bottom",
                      backgroundRepeat: "no-repeat",
                    }}
                  />
                  {isSelected ? (
                    <span className="relative z-10 font-bold">
                      {isCorrect ? (
                        <span>正解!</span>
                      ) : (
                        <span className="text-[#d71d24]">殘念!</span>
                      )}
                    </span>
                  ) : (
                    <span className="relative z-10">{option}</span>
                  )}
                  {isSelected && isCorrect && (
                    <Image
                      src={imageUrl("/spark.png")}
                      alt=""
                      unoptimized
                      width={333}
                      height={359}
                      className="opacity-75 absolute w-[35vw] top-0 -right-[8vw]"
                    />
                  )}
                </button>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};
