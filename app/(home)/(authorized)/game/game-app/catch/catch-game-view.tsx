"use client";
import { useRef, useEffect, CSSProperties } from "react";
import Image from "next/image";
import { imageUrl } from "@/utils/image-url";
import { CatchGameState } from "./catch-game";
import { GameDisplay } from "../shared/game-display";

const formatTime = (time: number) => {
  const seconds = Math.floor(time / 1000);
  const milliseconds = Math.floor((time % 1000) / 10);
  return `${seconds.toString().padStart(2, "0")}:${milliseconds.toString().padStart(2, "0")}`;
};

interface CatchGameViewProps {
  gameState: CatchGameState | undefined;
  onUpdatePlayerPosition: (position: number) => void;
}

export const CatchGameView = ({
  gameState,
  onUpdatePlayerPosition,
}: CatchGameViewProps) => {
  const gameAreaRef = useRef<HTMLDivElement>(null);
  const isDraggingRef = useRef(false);

  const { remainTime = 0, score = 0, items = [], player } = gameState ?? {};

  // Handle mouse/touch events for player movement
  const handlePointerDown = (e: React.PointerEvent) => {
    isDraggingRef.current = true;
    updatePlayerPosition(e);
  };

  const handlePointerMove = (e: React.PointerEvent) => {
    if (isDraggingRef.current) {
      updatePlayerPosition(e);
    }
  };

  const handlePointerUp = () => {
    isDraggingRef.current = false;
  };

  const updatePlayerPosition = (e: React.PointerEvent) => {
    if (!gameAreaRef.current) return;

    const rect = gameAreaRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = (x / rect.width) * 100;
    const clampedPercentage = Math.max(0, Math.min(100, percentage));

    onUpdatePlayerPosition(clampedPercentage);
  };

  // Add global pointer event listeners
  useEffect(() => {
    const handleGlobalPointerMove = (e: PointerEvent) => {
      if (isDraggingRef.current && gameAreaRef.current) {
        const rect = gameAreaRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = (x / rect.width) * 100;
        const clampedPercentage = Math.max(0, Math.min(100, percentage));
        onUpdatePlayerPosition(clampedPercentage);
      }
    };

    const handleGlobalPointerUp = () => {
      isDraggingRef.current = false;
    };

    document.addEventListener("pointermove", handleGlobalPointerMove);
    document.addEventListener("pointerup", handleGlobalPointerUp);

    return () => {
      document.removeEventListener("pointermove", handleGlobalPointerMove);
      document.removeEventListener("pointerup", handleGlobalPointerUp);
    };
  }, [onUpdatePlayerPosition]);

  return (
    <div className="w-full flex flex-col items-center">
      {/* Score Board */}
      <div className="relative w-[70vw] h-[11vw] mt-[5.5vw]">
        <Image
          unoptimized
          alt=""
          src={imageUrl("/game-score-background.png")}
          width={756}
          height={121}
        />
        <div className="absolute top-[3.9vw] left-[3vw] flex">
          <GameDisplay title="TIME" value={formatTime(remainTime)} />
          <GameDisplay title="SCORE" value={score} className="ml-[4vw]" />
        </div>
      </div>

      {/* Game Area */}
      <div
        ref={gameAreaRef}
        style={
          {
            userSelect: "none",
            "--view-width": "85vw",
            "--view-height": "100vw",
            width: "var(--view-width)",
            height: "var(--view-height)",
          } as CSSProperties
        }
        className="relative mt-[5vw] rounded-lg overflow-hidden touch-none"
        onPointerDown={handlePointerDown}
        onPointerMove={handlePointerMove}
        onPointerUp={handlePointerUp}
      >
        {/* Falling Items */}
        {items.map((item) => (
          <div
            key={item.id}
            className="absolute"
            style={{
              transform: `translate(calc(${item.position.x}/100 * var(--view-width) + -50%), calc(${item.position.y}/100 * var(--view-height) + -80%))`,
            }}
          >
            <Image
              key={item.id}
              unoptimized
              alt=""
              src={imageUrl(item.image.src)}
              width={item.image.width}
              height={item.image.height}
              className="drop-shadow-md"
              style={{
                width: `calc(${item.image.width} / 1500 * var(--view-width))`,
                height: `calc(${item.image.height} / 1500 * var(--view-width))`,
              }}
            />
          </div>
        ))}

        {/* Player Basket */}
        {player && (
          <div
            className="absolute"
            style={{
              top: `calc(${player.position.y}/100 * var(--view-height))`,
              transform: `translateX(calc(${player.position.x}/100 * var(--view-width)))`,
            }}
          >
            <div
              className="relative"
              style={{
                width: `${player.width}vw`,
                height: `${player.height}vw`,
                transform: `translateX(-50%)`,
              }}
            >
              <Image
                unoptimized
                alt=""
                src={imageUrl("/logo-button.png")}
                width={419}
                height={214}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
