// Styling Cross-Browser Compatible Range Inputs with Sass
// Github: https://github.com/darlanrod/input-range-sass
// Author: <PERSON><PERSON> https://github.com/darlanrod
// Version 1.6.0
// MIT License
@use "sass:math";
@use "sass:color";
@import url(https://db.onlinewebfonts.com/c/6daafe4bf5f24958dc640da592c230c4?family=MStiffHeiHK-UltraBold);

$track-color: #ffffff !default;
$thumb-color: #ffffff !default;

$thumb-radius: 15vw !default;
$thumb-height: 30vw !default;
$thumb-width: 15vw !default;
$thumb-shadow-size: 4px !default;
$thumb-shadow-blur: 4px !default;
$thumb-shadow-color: rgba(0, 0, 0, 0.2) !default;
$thumb-border-width: 2px !default;
$thumb-border-color: transparent !default;

$track-width: 100% !default;
$track-height: 8px !default;
$track-shadow-size: 1px !default;
$track-shadow-blur: 1px !default;
$track-shadow-color: rgba(0, 0, 0, 0.2) !default;
$track-border-width: 2px !default;
$track-border-color: transparent !default;

$track-radius: 5px !default;
$contrast: 5% !default;

$ie-bottom-track-color: color.adjust(
  $track-color,
  $lightness: -$contrast
) !default;

@mixin shadow($shadow-size, $shadow-blur, $shadow-color) {
  box-shadow:
    $shadow-size $shadow-size $shadow-blur $shadow-color,
    0 0 $shadow-size color.adjust($shadow-color, $lightness: 5%);
}

@mixin track {
  cursor: default;
  height: $track-height;
  transition: all 0.2s ease;
  width: $track-width;
}

@mixin thumb {
  @include shadow($thumb-shadow-size, $thumb-shadow-blur, $thumb-shadow-color);
  background: $thumb-color;
  border: $thumb-border-width solid $thumb-border-color;
  border-radius: $thumb-radius;
  box-sizing: border-box;
  cursor: default;
  height: $thumb-height;
  width: $thumb-width;
}

[type="range"] {
  -webkit-appearance: none;
  background: transparent;
  margin: math.div($thumb-height, 2) 0;
  width: $track-width;

  &::-moz-focus-outer {
    border: 0;
  }

  &:focus {
    outline: 0;

    &::-webkit-slider-runnable-track {
      background: color.adjust($track-color, $lightness: $contrast);
    }

    &::-ms-fill-lower {
      background: $track-color;
    }

    &::-ms-fill-upper {
      background: color.adjust($track-color, $lightness: $contrast);
    }
  }

  &:focus-visible {
    outline: 2px solid color.adjust($thumb-color, $lightness: 20%);
    outline-offset: 2px;
  }

  &::-webkit-slider-runnable-track {
    @include track;
    @include shadow(
      $track-shadow-size,
      $track-shadow-blur,
      $track-shadow-color
    );
    background: $track-color;
    border: $track-border-width solid $track-border-color;
    border-radius: $track-radius;

    &:active {
      background: color.adjust($track-color, $lightness: $contrast);
    }
  }

  &::-webkit-slider-thumb {
    @include thumb;
    -webkit-appearance: none;
    margin-top: calc((#{$track-height} - #{$thumb-height}) / 2);

    &:hover {
      background: color.adjust($thumb-color, $lightness: 10%);
    }
  }

  &::-moz-range-track {
    @include shadow(
      $track-shadow-size,
      $track-shadow-blur,
      $track-shadow-color
    );
    @include track;
    background: $track-color;
    border: $track-border-width solid $track-border-color;
    border-radius: $track-radius;
    height: math.div($track-height, 2);

    &:active {
      background: color.adjust($track-color, $lightness: $contrast);
    }
  }

  &::-moz-range-thumb {
    @include thumb;

    &:hover {
      background: color.adjust($thumb-color, $lightness: 10%);
    }
  }

  &::-ms-track {
    @include track;
    background: transparent;
    border-color: transparent;
    border-width: math.div($thumb-height, 2) 0;
    color: transparent;
  }

  &::-ms-fill-lower {
    @include shadow(
      $track-shadow-size,
      $track-shadow-blur,
      $track-shadow-color
    );
    background: $ie-bottom-track-color;
    border: $track-border-width solid $track-border-color;
    border-radius: ($track-radius * 2);

    &:active {
      background: color.adjust($track-color, $lightness: $contrast);
    }
  }

  &::-ms-fill-upper {
    @include shadow(
      $track-shadow-size,
      $track-shadow-blur,
      $track-shadow-color
    );
    background: $track-color;
    border: $track-border-width solid $track-border-color;
    border-radius: ($track-radius * 2);

    &:active {
      background: color.adjust($track-color, $lightness: $contrast);
    }
  }

  &::-ms-thumb {
    @include thumb;
    margin-top: math.div($track-height, 4);

    &:hover {
      background: color.adjust($thumb-color, $lightness: 10%);
    }
  }

  &:disabled {
    opacity: 0.5;

    &::-webkit-slider-thumb,
    &::-moz-range-thumb,
    &::-ms-thumb,
    &::-webkit-slider-runnable-track,
    &::-ms-fill-lower,
    &::-ms-fill-upper {
      cursor: not-allowed;
    }
  }
}
