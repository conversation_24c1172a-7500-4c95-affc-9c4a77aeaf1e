import { createNoise2D } from "simplex-noise";

export interface BalanceGameState {
  passedTime: number;
  position: number;
  force: number;
  player: {
    position: number;
    force: number;
  };
}

type FuncUpdateUi = (args: BalanceGameState) => void;

export class BalanceGame {
  noise = createNoise2D();
  startTime = Date.now();
  passedTime = 0;
  position = 0;
  animationRequestId = 0;
  force = 0;

  player = {
    position: 0,
    force: 0.5,
  };

  onUpdateUi: FuncUpdateUi;
  onGameEnd: (score: number) => void;

  constructor({
    onUpdateUi,
    onGameEnd,
  }: {
    onUpdateUi: FuncUpdateUi;
    onGameEnd: (score: number) => void;
  }) {
    this.onUpdateUi = onUpdateUi;
    this.onGameEnd = onGameEnd;
  }

  start = () => {
    setTimeout(() => {
      this.startTime = Date.now();
      this.animationRequestId = requestAnimationFrame(this.update);
    }, 500);
  };

  onGameOver = () => {
    this.onGameEnd(Math.floor(this.passedTime / 1000));
  };

  onUpdatePlayerPosition = (position: number) => {
    this.player.position = position;
  };

  update = () => {
    const newPassedTime = Date.now() - this.startTime;
    const isInvalidDuration = newPassedTime - this.passedTime > 1000;

    if (isInvalidDuration) {
      window.cancelAnimationFrame(this.animationRequestId);
      this.onGameOver();
      return;
    }

    this.passedTime = newPassedTime;
    this.force =
      this.noise(this.passedTime / 1000, 0) *
        Math.min(10, 1 + this.passedTime / 2000) -
      (this.player.force * this.player.position) / 10;

    this.position += this.force;

    this.onUpdateUi({
      passedTime: this.passedTime,
      position: this.position,
      force: this.force,
      player: this.player,
    });

    const isOutOfBound = Math.abs(this.position) > 100;
    const isTimeout = this.passedTime >= 60 * 1000;

    if (isOutOfBound || isTimeout) {
      window.cancelAnimationFrame(this.animationRequestId);
      this.onGameOver();
      return;
    } else {
      this.animationRequestId = requestAnimationFrame(this.update);
    }
  };
}
