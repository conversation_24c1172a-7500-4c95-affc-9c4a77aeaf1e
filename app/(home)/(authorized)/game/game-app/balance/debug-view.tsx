import { BalanceGameState } from "./balance-game";

export const BalanceGameDebugView = ({
  gameState,
  onUpdateUserPosition,
}: {
  gameState: BalanceGameState | undefined;
  onUpdateUserPosition: (position: number) => void;
}) => {
  const {
    force = 0,
    passedTime = 0,
    position = 0,
    player = {
      position: 0,
      force: 0,
    },
  } = gameState ?? {};

  const rotateStyle = { transform: `rotateZ(${force * 5}deg)` };

  return (
    <div className="w-full">
      <div>
        <div>Passed Time: {(passedTime / 1000).toFixed(2)}</div>
        <div>Position: {position.toFixed(2)}</div>
        <div>Force: {force.toFixed(2)}</div>
      </div>

      <div>
        <div
          className="w-[60vw] flex flex-col items-center"
          style={rotateStyle}
        >
          <div
            className="w-[15vw] h-[15vw] rounded-full bg-white relative"
            style={{
              transformOrigin: "center bottom",
              transform: `translateX(calc(${position}/200 * 60vw))`,
            }}
          />
        </div>
      </div>

      <div className="w-[60vw] flex flex-col items-center" style={rotateStyle}>
        <div className="w-full rounded-full w-[70vw] h-[1vw] bg-white" />
      </div>

      <div className="w-full touch-none flex flex-col items-center mt-[5vw]">
        <div
          className="w-[15vw] h-[30vw] bg-white"
          style={{
            transform: `translateX(calc(${player.position}/200 * 60vw))`,
          }}
        />
        <input
          className="relative z-10 min-w-[75vw]"
          style={{ margin: "0 -12.5vw", opacity: 0.001 }}
          type="range"
          min="-100"
          max="100"
          onChange={(e) => {
            const value = Number(e.target.value ?? 0);
            onUpdateUserPosition(value);
          }}
        />
      </div>
    </div>
  );
};
