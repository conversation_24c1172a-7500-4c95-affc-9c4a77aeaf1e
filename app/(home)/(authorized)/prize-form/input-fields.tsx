import clsx from "clsx";
import { HTMLInputTypeAttribute, PropsWithChildren, useState } from "react";

interface TextInputProps {
  name: string;
  label: string;
  type?: HTMLInputTypeAttribute;
  minLength?: number;
}
export const TextInput = (props: TextInputProps) => {
  const { name, label, type = "text", minLength } = props;

  const [focused, setFocused] = useState(false);

  return (
    <label
      className={clsx(
        "flx gap-[2vw] border-1 flex text-[3.5vw] rounded-[2vw] py-[1.8vw] px-[3vw] border-[var(--primary-color)]",
        {
          ["outline-2 outline-red-500"]: focused,
        },
      )}
    >
      <span className="w-[12vw] shrink-0 text-left">{label}：</span>
      <input
        className="w-[30vw] grow focus:outline-none"
        type={type}
        name={name}
        required
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        minLength={minLength}
      />
    </label>
  );
};

export const Checkbox = ({
  children,
}: PropsWithChildren<{ className?: string }>) => {
  const [checked, setChecked] = useState(false);
  return (
    <label className="flex text-[#fff100]">
      <input
        name="agree"
        className="absolute opacity-0"
        type="checkbox"
        onChange={(e) => setChecked(e.target.checked)}
        required
      />
      <span className="w-[4.6vw] h-[4.6vw] inline-block border-1 rounded-[1.5vw] border-currentColor mr-[0.5em] font-medium">
        <div className="flex justify-center items-center leading-[1em] text-[3.3vw]">
          {checked ? "✔︎" : ""}
        </div>
      </span>
      {children}
    </label>
  );
};
