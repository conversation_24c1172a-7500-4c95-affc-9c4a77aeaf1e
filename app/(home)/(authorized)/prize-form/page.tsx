"use client";
import { useState } from "react";
import { imageUrl } from "@/utils/image-url";
import { AppButton } from "@/app/components/buttons/app-button";
import { FullScreenImage } from "@/app/components/full-screen";
import { ContentArea } from "@/app/components/content-area";
import { Recaptcha } from "@/app/components/recaptcha";
import { TextInput, Checkbox } from "./input-fields";
import { SuccessDialog } from "./success-dialog";
import { redirect } from "next/navigation";
import { useCheckGameCompletion } from "../../../hooks/use-check-game-completion";
import { ctaClickEvent } from "@/utils/ga-event";

const PersonalPage = () => {
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { allGamesComplete: hasUserCompletedGames } =
    useCheckGameCompletion() ?? {};

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    ctaClickEvent({ label: "登入抽獎_送出資料" });

    if (!recaptchaToken) {
      alert("請完成人機驗證");
      return;
    }

    const formData = new FormData(e.target as HTMLFormElement);
    const data = Object.fromEntries(formData);

    // Validate required fields
    if (
      !data.luckyDrawName ||
      !data.luckyDrawAge ||
      !data.luckyDrawPhone ||
      !data.luckyDrawAddress
    ) {
      alert("請填寫所有必要欄位");
      return;
    }

    // Validate privacy agreement
    if (!data.agree) {
      alert("請同意個資隱私條款");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/prize-form/submit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          luckyDrawName: data.luckyDrawName,
          luckyDrawAge: data.luckyDrawAge,
          luckyDrawPhone: data.luckyDrawPhone,
          luckyDrawAddress: data.luckyDrawAddress,
          recaptchaToken,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setShowSuccess(true);
      } else {
        alert(result.message || "提交失敗，請稍後再試");
      }
    } catch (error) {
      console.error("Form submission error:", error);
      alert("網絡錯誤，請稍後再試");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!hasUserCompletedGames) {
    return null;
  }

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-page.png")} />
      <div className="flex flex-col items-center relative pt-[35vw] text-center">
        <ContentArea className="h-[116vw] pt-[5vw] pb-[5vw]">
          <h2 className="text-[6vw] font-[1000] text-[var(--primary-color)] mb-[1.5vw]">
            請填寫抽獎資料
          </h2>

          <form
            name="prize-form"
            className="flex flex-col gap-[3vw]"
            onSubmit={onSubmit}
          >
            <div className="w-[60vw] mx-auto flex flex-col gap-[3vw]">
              <TextInput name="luckyDrawName" label="姓名" />
              <TextInput name="luckyDrawAge" label="年齡" type="number" />
              <TextInput
                name="luckyDrawPhone"
                label="電話"
                type="tel"
                minLength={6}
              />
              <TextInput name="luckyDrawAddress" label="地址" />
            </div>

            <div className="mx-auto">
              <Checkbox className="text-[#fff100]">
                <a
                  href="/rule"
                  target="_blank"
                  className="underline underline-offset-2 leading-[1em]"
                >
                  個資隱私同意條款
                </a>
              </Checkbox>
            </div>

            <div className="h-[68px]">
              <Recaptcha
                className="mx-auto scale-80"
                onVerify={(token) => setRecaptchaToken(token || "")}
                size="normal"
                theme="light"
              />
            </div>

            <AppButton
              className="mx-auto"
              disabled={!recaptchaToken || isSubmitting}
            >
              {isSubmitting ? "提交中..." : "送出資料"}
            </AppButton>
          </form>
        </ContentArea>
      </div>
      {showSuccess && (
        <SuccessDialog
          onClose={() => {
            setShowSuccess(false);
            redirect("/home");
          }}
        />
      )}
    </>
  );
};

export default PersonalPage;
