"use client";
import { FullScreenImage } from "@/app/components/full-screen";

import { AppButton } from "@/app/components/buttons/app-button";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";
import clsx from "clsx";
import { ContentArea } from "../../../components/content-area";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { pageViewEvent } from "@/utils/ga-event";

// Types for user's personal coupons
interface UserCoupon {
  id: string;
  code: string;
  createdAt: string;
  updatedAt: string;
}

// Service for fetching user's personal coupons
const userCouponService = {
  getMyCoupons: async (): Promise<UserCoupon[]> => {
    const response = await fetch("/api/coupon/my");

    if (!response.ok) {
      throw new Error("獲取獎券失敗");
    }

    return response.json();
  },
};

// React Query hook for user's personal coupons
const useUserCoupons = () => {
  return useQuery({
    queryKey: ["userCoupons", "my"],
    queryFn: () => userCouponService.getMyCoupons(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

const PersonalPage = () => {
  const { data: userCoupons, isLoading, error } = useUserCoupons();

  useEffect(() => {
    pageViewEvent({ label: "個人頁面" });
  }, []);

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-page.png")} />
      <div className="flex flex-col items-center relative pt-[35vw] text-center">
        <ContentArea className="h-[116vw] pt-[5vw] pb-[10vw]">
          <div className="text-[#fff100] font-[1000] text-[5.3vw] leading-tight mb-[3vw]">
            我的序號
          </div>

          <div className="font-[900] text-[4vw] mb-[1.5vw]">
            憑PIN碼至全台全家門市，即可兌換
            <br />
            威金森碳酸水PET500ml 1瓶
          </div>

          <div className="text-[#fff100] text-[2.8vw] font-bold mb-[3.5vw]">
            兌換期間：即日起~2025/8/30(六)止
          </div>

          <div className="flex flex-col gap-[5vw] mb-[6vw]">
            {isLoading ? (
              <div className="text-[4vw] text-white">載入中...</div>
            ) : error ? (
              <div className="text-[4vw] text-red-400">
                載入失敗，請重新整理頁面
              </div>
            ) : !userCoupons || userCoupons.length === 0 ? (
              <div className="text-[4vw] text-white">尚未獲得任何PIN碼</div>
            ) : (
              userCoupons.map((coupon) => {
                return (
                  <div
                    key={coupon.id}
                    className={clsx(
                      "bg-center bg-contain bg-no-repeat",
                      "aspect-[685/138] w-[64vw]",
                      "flex items-center px-[4vw]",
                    )}
                    style={{
                      backgroundImage: `url(${imageUrl("/voucher-background.png")})`,
                    }}
                  >
                    <Image
                      unoptimized
                      priority
                      className="h-[11vw] w-auto mr-[3vw]"
                      src={imageUrl("/voucher-bottle.png")}
                      alt=""
                      width={40}
                      height={122}
                    />
                    <span className="font-[900] ml-[3vw] text-[3.5vw]">
                      {coupon.code}
                    </span>
                  </div>
                );
              })
            )}
          </div>

          <a href="https://nevent.family.com.tw/fami_pin/" target="_blank">
            <AppButton as="div">如何兌換</AppButton>
          </a>
        </ContentArea>
      </div>
    </>
  );
};

export default PersonalPage;
