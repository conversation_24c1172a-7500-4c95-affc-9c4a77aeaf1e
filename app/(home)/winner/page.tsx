"use client";
import { FullScreenImage } from "@/app/components/full-screen";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";
import clsx from "clsx";
import { useQuery } from "@tanstack/react-query";
import type { WebsiteConfig } from "@/app/api/event/query";
import { useEffect } from "react";
import { pageViewEvent } from "@/utils/ga-event";

const WinnerPage = () => {
  const { data, isLoading } = useQuery({
    queryKey: ["websiteConfig"],
    queryFn: () =>
      fetch("/api/event").then((res) => res.json()) as Promise<WebsiteConfig>,
  });

  useEffect(() => {
    pageViewEvent({ label: "得獎名單" });
  }, []);

  const { revealDate, prizeOwnerName, prizeOwnerPhone } = data ?? {};

  const date = revealDate ? new Date(revealDate) : undefined;

  const isPrizeRevealed =
    !!revealDate &&
    Date.now() >= new Date(revealDate).getTime() &&
    !!prizeOwnerName;

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-cm.png")} />
      <div className="flex flex-col items-center relative pt-[35vw] text-center">
        <div
          className={clsx(
            "w-[76vw] h-[116vw] flex flex-col items-center pt-[7.5vw] pb-[10vw] overflow-auto",
          )}
        >
          <Image
            unoptimized
            priority
            className="w-[50vw] mb-[12vw]"
            src={imageUrl("/prize-1st-title.png")}
            alt=""
            width={495}
            height={188}
          />

          <div className="w-full relative">
            <div
              className={clsx(
                "text-[#fff100] font-[1000] text-[5.3vw] leading-tight mb-[3vw] whitespace-pre",
                { hidden: isLoading },
              )}
            >
              {isPrizeRevealed
                ? `${prizeOwnerName}\n${prizeOwnerPhone}`
                : date
                  ? `將於\n${date.getMonth() + 1}月${date.getDate()}日公布`
                  : "尚未公布"}
            </div>
            <Image
              unoptimized
              priority
              className="w-[32vw] absolute right-[4vw] top-[-3.5vw]"
              src={imageUrl("/prize-1st.png")}
              width={344}
              height={382}
              alt=""
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default WinnerPage;
