"use client";
import { imageUrl } from "@/utils/image-url";
import { FullScreenImage } from "../../components/full-screen";
import Link from "next/link";
import { AppButton } from "../../components/buttons/app-button";
import { DialogOpenInBrowser } from "../../components/dialog-open-in-browser";
import { useImagePreloader } from "../../hooks/use-image-preloader";
import { useEffect } from "react";
import { pageViewEvent } from "@/utils/ga-event";

const StartPage = () => {
  useImagePreloader();

  useEffect(() => {
    pageViewEvent({ label: "首頁_Start" });
  }, []);
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-start.png")} />
      <div className="flex justify-center pt-[80vw]">
        <Link
          href="/game"
          className="relative z-0 w-[39vw] aspect-[3] active:opacity-80 flex justify-center items-center cursor-pointer"
        >
          <AppButton as="div">點擊開始</AppButton>
        </Link>
      </div>
      <DialogOpenInBrowser />
    </>
  );
};

export default StartPage;
