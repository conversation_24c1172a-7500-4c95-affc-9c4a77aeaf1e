"use client";
import { PropsWithChildren } from "react";
import { WebsiteLayout } from "../components/layouts/website-layout";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

export default function Home({ children }: PropsWithChildren) {
  return (
    <WebsiteLayout>
      <QueryClientProvider
        client={
          new QueryClient({
            defaultOptions: {
              queries: {
                refetchOnWindowFocus: false,
              },
            },
          })
        }
      >
        {children}
      </QueryClientProvider>
    </WebsiteLayout>
  );
}
