"use client";

import { FullScreenImage } from "@/app/components/full-screen";
import { GameTitle } from "../(authorized)/game/components";
import { imageUrl } from "@/utils/image-url";
import { GameStartWipe } from "../(authorized)/game/stages/game-start-wipe";

const GamePage = () => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-game.png")} />
      <div className="relative z-0 flex flex-col items-center pt-[4vw]">
        <div className="w-[73vw] h-[23.5vw] flex justify-center items-center">
          <GameTitle gameId={"balance"} />
        </div>
      </div>
      <GameStartWipe />
    </>
  );
};

export default GamePage;
