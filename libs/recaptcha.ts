/**
 * Google reCAPTCHA verification utility
 */

interface RecaptchaResponse {
  success: boolean;
  challenge_ts?: string;
  hostname?: string;
  "error-codes"?: string[];
}

/**
 * Verify reCAP<PERSON><PERSON> token on the server side
 * @param token - The reCAPTCHA token from the client
 * @returns Promise<boolean> - Whether the verification was successful
 */
export async function verifyRecaptcha(token: string): Promise<boolean> {
  const secretKey = process.env.GOOGLE_RECAPTCHA_SECRET_KEY;

  if (!secretKey) {
    console.error("GOOGLE_RECAPTCHA_SECRET_KEY is not configured");
    return false;
  }

  if (!token) {
    console.error("reCAP<PERSON>HA token is missing");
    return false;
  }

  try {
    const response = await fetch(
      new URL(
        "https://www.google.com/recaptcha/api/siteverify?" +
          new URLSearchParams({
            secret: secretKey,
            response: token,
          }),
      ),
      {
        method: "POST",
      },
    );

    if (!response.ok) {
      console.error("reCAPTCHA verification request failed:", response.status);
      return false;
    }

    const data: RecaptchaResponse = await response.json();

    if (!data.success) {
      console.error("reCAPTCHA verification failed:", data["error-codes"]);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error verifying reCAPTCHA:", error);
    return false;
  }
}

/**
 * Get the reCAPTCHA site key for client-side usage
 */
export function getRecaptchaSiteKey(): string {
  const siteKey = process.env.NEXT_PUBLIC_GOOGLE_RECAPTCHA_SITE_KEY;

  if (!siteKey) {
    throw new Error("NEXT_PUBLIC_GOOGLE_RECAPTCHA_SITE_KEY is not configured");
  }

  return siteKey;
}
