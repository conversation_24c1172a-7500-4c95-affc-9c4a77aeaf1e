// script/upload-public-r2.js
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import * as fs from "fs";
import * as path from "path";

const accountId = process.env.R2_ACCOUNT_ID;
const accessKeyId = process.env.R2_ACCESS_KEY_ID;
const secretAccessKey = process.env.R2_SECRET_ACCESS_KEY;
const bucketName = process.env.R2_BUCKET_NAME;
const publicDir = "./public"; // Assuming the script is run from the project root

if (!accountId || !accessKeyId || !secretAccessKey || !bucketName) {
  console.error(
    "請設定 R2_ACCOUNT_ID, R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY, 和 R2_BUCKET_NAME 環境變數。",
  );
  process.exit(1);
}

const R2 = new S3Client({
  region: "auto", // R2 不使用傳統的 AWS 區域
  endpoint: `https://${accountId}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: accessKeyId,
    secretAccessKey: secretAccessKey,
  },
});

async function uploadFile(filePath, bucketKey) {
  try {
    const fileContent = fs.readFileSync(filePath);
    const params = {
      Bucket: bucketName,
      Key: bucketKey,
      Body: fileContent,
      // 你可以根據檔案類型設定 ContentType，例如使用 'mime-types' 套件
      // ContentType: 'image/png'
    };
    const command = new PutObjectCommand(params);
    await R2.send(command);
    console.log(`成功上傳 ${filePath} 至 ${bucketKey}`);
  } catch (err) {
    console.error(`上傳 ${filePath} 失敗:`, err);
  }
}

async function uploadPublicDirectory() {
  try {
    const files = fs.readdirSync(publicDir);
    for (const file of files) {
      const filePath = path.join(publicDir, file);
      // For simplicity, uploading root files directly.
      // You might need to recursively handle subdirectories.
      await uploadFile(filePath, file);
    }
    console.log("Public 目錄檔案上傳完成。");
  } catch (err) {
    console.error("讀取 Public 目錄失敗:", err);
  }
}

uploadPublicDirectory();
